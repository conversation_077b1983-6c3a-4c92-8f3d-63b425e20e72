/**
 * Ported structure tests from the original lexical-parser
 * These tests ensure comprehensive structural element support in the new Markdown class
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Markdown } from '../../src/markdown';

describe('Ported Structure Tests', () => {
  let parser: Markdown;

  beforeEach(() => {
    parser = new Markdown();
  });

  afterEach(() => {
    parser.destroy();
  });

  describe('Headings', () => {
    it('should convert all heading levels', async () => {
      const markdown = '# H1\n## H2\n### H3\n#### H4\n##### H5\n###### H6';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(6);

      const headings = result.data?.root.children as any[];
      expect(headings[0].type).toBe('extended-heading');
      expect(headings[0].tag).toBe('h1');
      expect(headings[1].tag).toBe('h2');
      expect(headings[2].tag).toBe('h3');
      expect(headings[3].tag).toBe('h4');
      expect(headings[4].tag).toBe('h5');
      expect(headings[5].tag).toBe('h6');
    });

    it('should handle headings with formatting', async () => {
      const markdown = '# **Bold** Heading with *italic*';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const heading = result.data?.root.children[0] as any;
      expect(heading.type).toBe('extended-heading');
      expect(heading.tag).toBe('h1');
      expect(heading.children.length).toBeGreaterThan(1);
    });

    it('should handle headings with special characters', async () => {
      const markdown = '# Heading with "quotes" and symbols!@#$%';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const heading = result.data?.root.children[0] as any;
      expect(heading.type).toBe('extended-heading');
      expect(heading.tag).toBe('h1');
    });
  });

  describe('Lists', () => {
    it('should convert unordered lists', async () => {
      const markdown = '- Item 1\n- Item 2\n- Item 3';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);

      const list = result.data?.root.children[0] as any;
      expect(list.type).toBe('list');
      expect(list.listType).toBe('bullet');
      expect(list.children).toHaveLength(3);
    });

    it('should convert ordered lists', async () => {
      const markdown = '1. First item\n2. Second item\n3. Third item';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);

      const list = result.data?.root.children[0] as any;
      expect(list.type).toBe('list');
      expect(list.listType).toBe('number');
      expect(list.children).toHaveLength(3);
    });

    it('should handle nested lists', async () => {
      const markdown = `- Item 1
  - Nested item 1
  - Nested item 2
    - Double nested
- Item 2`;

      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('list');
    });

    it('should handle lists with formatting', async () => {
      const markdown = `1. **Bold item**
2. *Italic item*
3. \`Code item\`
4. [Link item](https://example.com)`;

      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const listNode = result.data?.root.children[0] as any;
      expect(listNode.type).toBe('list');
      expect(listNode.listType).toBe('number');
    });

    it('should handle mixed list types', async () => {
      const markdown = `- Unordered item
1. Ordered item
- Another unordered`;

      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should create separate lists for different types
      expect(result.data?.root.children.length).toBeGreaterThan(1);
    });
  });

  describe('Links', () => {
    it('should convert inline links', async () => {
      const markdown = 'This is a [link](https://example.com) in text.';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);

      const linkNode = paragraph.children.find((node: any) => node.type === 'link');
      expect(linkNode).toBeDefined();
      expect(linkNode.url).toBe('https://example.com');
    });

    it('should convert links with titles', async () => {
      const markdown = '[Link with title](https://example.com "Example Title")';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      const linkNode = paragraph.children[0] as any;
      expect(linkNode.type).toBe('link');
      expect(linkNode.url).toBe('https://example.com');
      expect(linkNode.title).toBe('Example Title');
    });

    it('should handle autolinks', async () => {
      const markdown = 'Visit https://example.com for more info.';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should parse successfully even if autolinks aren't converted
    });

    it('should handle email links', async () => {
      const markdown = 'Contact us at [email](mailto:<EMAIL>)';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      const linkNode = paragraph.children.find((node: any) => node.type === 'link');
      expect(linkNode).toBeDefined();
      expect(linkNode.url).toBe('mailto:<EMAIL>');
    });
  });

  describe('Code Blocks', () => {
    it('should convert fenced code blocks', async () => {
      const markdown = '```javascript\nconsole.log("Hello, world!");\n```';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const codeBlock = result.data?.root.children[0] as any;
      expect(codeBlock.type).toBe('code');
      expect(codeBlock.language).toBe('javascript');
    });

    it('should convert code blocks without language', async () => {
      const markdown = '```\nsome code\nmore code\n```';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const codeBlock = result.data?.root.children[0] as any;
      expect(codeBlock.type).toBe('code');
    });

    it('should handle indented code blocks', async () => {
      const markdown = '    indented code\n    more indented code';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should handle indented code blocks
    });

    it('should handle code blocks with special characters', async () => {
      const markdown = '```\n<html>\n  <body>Hello & "world"</body>\n</html>\n```';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const codeBlock = result.data?.root.children[0] as any;
      expect(codeBlock.type).toBe('code');
    });
  });

  describe('Blockquotes', () => {
    it('should convert simple blockquotes', async () => {
      const markdown = '> This is a blockquote';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const quote = result.data?.root.children[0] as any;
      expect(quote.type).toBe('quote');
    });

    it('should convert multi-line blockquotes', async () => {
      const markdown = '> Line 1\n> Line 2\n> Line 3';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const quote = result.data?.root.children[0] as any;
      expect(quote.type).toBe('quote');
      expect(quote.children.length).toBeGreaterThan(0);
    });

    it('should handle nested blockquotes', async () => {
      const markdown = '> Outer quote\n> > Nested quote\n> Back to outer';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const quote = result.data?.root.children[0] as any;
      expect(quote.type).toBe('quote');
    });

    it('should handle blockquotes with formatting', async () => {
      const markdown = '> This is **bold** in a blockquote';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const quote = result.data?.root.children[0] as any;
      expect(quote.type).toBe('quote');
    });
  });

  describe('Mixed Structures', () => {
    it('should handle complex mixed content', async () => {
      const markdown = `# Heading

This is a paragraph with **bold** text.

- List item 1
- List item 2

\`\`\`javascript
console.log("code block");
\`\`\`

> This is a blockquote

[Link](https://example.com)`;

      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(5);

      // Should have different types of nodes
      const nodeTypes = result.data?.root.children.map((child: any) => child.type);
      expect(nodeTypes).toContain('extended-heading');
      expect(nodeTypes).toContain('paragraph');
      expect(nodeTypes).toContain('list');
      expect(nodeTypes).toContain('code');
      expect(nodeTypes).toContain('quote');
    });

    it('should handle lists within blockquotes', async () => {
      const markdown = `> This is a quote with a list:
> - Item 1
> - Item 2`;

      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const quote = result.data?.root.children[0] as any;
      expect(quote.type).toBe('quote');
    });

    it('should handle code blocks in lists', async () => {
      const markdown = `1. First item
2. Code example:
   \`\`\`
   code here
   \`\`\`
3. Third item`;

      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should handle gracefully
    });
  });
});
