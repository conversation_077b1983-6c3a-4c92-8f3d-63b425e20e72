# Ghost Sync Plugin for Obsidian

A modern Obsidian plugin that uses reusable Ghost.io API modules to sync posts between Obsidian and Ghost.io.

## Features

- **Sync Status View**: Dedicated right sidepanel tab showing real-time sync status of the current post
- **Newsletter Publishing**: Publish posts with newsletter support and email sending capabilities
- **Email Safety Features**: Built-in recipient verification and test mode to prevent accidental sends
- **Email Sent Tracking**: Track which posts have been sent via newsletter with sync status
- **Sync to Ghost**: Send your current Obsidian note to Ghost.io as a published post
- **Sync from Ghost**: Pull a specific post from Ghost.io to your local articles directory
- **Sync all posts**: Download all posts from Ghost.io to your local articles directory
- **Direct API integration**: Uses reusable Ghost API modules directly (no shell commands)
- **Shared codebase**: Same modules used by CLI scripts and Obsidian plugin

## Prerequisites

- A Ghost.io site with Admin API access
- Your Ghost Admin API key (available in Ghost Admin → Integrations → Custom integrations)
- The reusable Ghost API modules must be available in `../lib/` directory

## Installation

1. Copy the plugin files to your Obsidian vault's `.obsidian/plugins/ghost-sync/` directory
2. Enable the plugin in Obsidian's Community Plugins settings
3. Configure the plugin settings:
   - **Ghost site URL**: Your Ghost site URL (e.g., `https://your-site.ghost.io`)
   - **Ghost Admin API Key**: Your Ghost Admin API key (format: `id:secret`)
   - **Articles directory**: Directory where your articles are stored (default: `articles`)
   - **Verbose output**: Enable detailed console logging

## Getting Your Ghost Admin API Key

1. Go to your Ghost Admin panel
2. Navigate to **Settings → Integrations**
3. Click **Add custom integration**
4. Give it a name (e.g., "Obsidian Sync")
5. Copy the **Admin API Key** (it should be in format: `id:secret`)
6. Paste it into the plugin settings

## Usage

### Ghost Sync Status View

The plugin provides a dedicated sync status view in the right sidepanel that shows:

- **Current file status**: Whether the active file is in the articles directory
- **Comprehensive sync status**: Shows if title, slug, status, tags, featured, newsletter, email sent status, and timestamps are synced with Ghost
- **Ghost post information**: Displays Ghost-side status, publication date, tags, featured status, newsletter, and timestamps
- **Newsletter publishing controls**: Publish posts with newsletter support and email sending
- **Email safety features**: Recipient verification and test mode to prevent accidental sends
- **Quick sync controls**: Buttons to sync to/from Ghost and refresh status

To open the sync status view:
- Click the sync status icon in the ribbon
- Use Command Palette: "Open Ghost Sync Status"

The view automatically updates when you switch between files and shows real-time sync status.

### Newsletter Publishing & Email Features

The plugin provides comprehensive newsletter publishing capabilities with built-in safety features:

#### Publishing Controls

In the sync status view, you'll find publishing controls that allow you to:

- **Publish without newsletter**: Standard post publishing
- **Publish with newsletter**: Send the post via email to subscribers
- **Newsletter selection**: Choose which newsletter to use for sending
- **Email segment targeting**: Target specific subscriber groups:
  - All subscribers
  - Free subscribers only
  - Paid subscribers only
  - Testers only (recommended for testing)

#### Email Safety Features

**🛡️ Built-in Safety Protections:**

- **Test Mode (Default)**: All publish requests are logged but never executed
- **Confirmation Dialog**: Shows detailed summary before any publishing action
- **Recipient Verification**: Shows exactly who would receive emails before sending
- **Member Count Display**: Shows total number of recipients for each segment
- **Email Preview**: Displays sample recipient email addresses
- **Email Already Sent Protection**: Prevents re-sending emails for posts already sent

**📋 Confirmation Dialog:**

When you click publish, a detailed confirmation dialog appears showing:

- **Post Details**: Title, status, and slug
- **Newsletter Information**: Selected newsletter and email segment
- **Recipient Summary**: Total count and sample email addresses
- **Safety Indicators**: Test mode status and warnings
- **Action Summary**: Exactly what will happen when you confirm

**Example Confirmation Dialog:**
```
┌─ Confirm Publishing ─────────────────────────┐
│ Post Details                                 │
│ Title: My Newsletter Post                    │
│ Status: published                            │
│ Slug: my-newsletter-post                     │
│                                              │
│ Newsletter Details                           │
│ Newsletter: Weekly Newsletter                │
│ Email Segment: label:tester                  │
│                                              │
│ Email Recipients                             │
│ Total Recipients: 2                          │
│ Sample Recipients:                           │
│ • <EMAIL>                           │
│ • <EMAIL>                         │
│                                              │
│ 🧪 Test Mode Active                          │
│ This is a test run. No actual emails will   │
│ be sent and no posts will be published.     │
│                                              │
│ What will happen:                            │
│ ✓ Log all request details to console        │
│ ✓ Show recipient verification information    │
│ ✗ No actual API calls will be made          │
│ ✗ No emails will be sent                    │
│                                              │
│           [Cancel]    [Run Test]             │
└──────────────────────────────────────────────┘
```

**📋 Console Verification:**

After confirmation, detailed information is logged to the console:

```
=== GHOST PUBLISH REQUEST (TEST MODE) ===
Newsletter Slug: weekly-newsletter
Email Segment: label:tester
Total recipients who would receive email: 2
Sample recipient emails: ["<EMAIL>", "<EMAIL>"]
✅ SAFE: Using tester filter - only test members will receive email
```

#### Email Sent Tracking

The plugin tracks which posts have been sent via newsletter:

- **Email Sent Status**: Shows "Yes/No" in sync status view
- **Frontmatter Sync**: Adds `Email Sent: true/false` to local files
- **Publish Prevention**: Disables newsletter publishing for posts already sent
- **Visual Indicators**: Button shows "Email Already Sent" when appropriate

### Syncing to Ghost

1. Open a markdown file in your articles directory
2. Make sure it has proper frontmatter with a `title` field
3. Use one of these methods:
   - Click the sync icon in the ribbon
   - Use Command Palette: "Sync current post to Ghost"
   - Use the "Sync to Ghost" button in the sync status view

### Syncing from Ghost

1. Use Command Palette: "Sync post from Ghost to local"
2. Enter the exact title of the post you want to sync
3. The post will be downloaded to your articles directory
4. Or use the "Sync from Ghost" button in the sync status view for the current file

### Syncing all posts

1. Use Command Palette: "Sync all posts from Ghost to local"
2. All posts will be downloaded to your articles directory

## File Format

The plugin expects and creates files in the following format:

```markdown
---
title: "Your Post Title"
date: "2025-03-14T11:05:21.000Z"
tags:
  - tag1
  - tag2
slug: your-post-slug
status: published
featured: false
visibility: public
Newsletter: "Weekly Newsletter"
Email Sent: false
Primary Tag: "tag1"
Feature Image: "https://example.com/image.jpg"
Created At: "2025-03-14T11:05:21.000Z"
Updated At: "2025-03-14T11:05:21.000Z"
Published At: "2025-03-14T11:05:21.000Z"
---

Your post content here...
```

### Frontmatter Fields

The plugin syncs the following fields between Obsidian and Ghost:

**Core Fields:**
- `title`: Post title
- `slug`: URL slug for the post
- `status`: Post status (draft, published, scheduled)
- `tags`: Array of tags
- `featured`: Whether the post is featured
- `visibility`: Post visibility (public, members, paid)

**Newsletter Fields:**
- `Newsletter`: Name of the newsletter (if post was sent via newsletter)
- `Email Sent`: Boolean indicating if post was sent via email

**Metadata Fields:**
- `Primary Tag`: The primary tag for the post
- `Feature Image`: URL of the featured image
- `Created At`: Post creation timestamp
- `Updated At`: Last update timestamp
- `Published At`: Publication timestamp

## Safety & Testing

### Newsletter Publishing Safety

**🚨 IMPORTANT: Test Mode is enabled by default to prevent accidental email sends.**

The plugin includes multiple safety layers:

1. **Test Mode Default**: All publish requests are logged but not executed
2. **Recipient Verification**: See exactly who would receive emails before sending
3. **Email Already Sent Protection**: Cannot re-send emails for posts already sent
4. **Tester Filter**: Use `label:tester` to send only to test members

### Testing Newsletter Features

1. **Create Test Members**: In Ghost Admin, create members with the "tester" label
2. **Use Test Mode**: Keep test mode enabled (default) for all testing
3. **Check Console**: Review detailed logs before any real sends
4. **Verify Recipients**: Confirm recipient count and email addresses
5. **Start Small**: Begin with tester-only sends before broader distribution

### Disabling Test Mode (Advanced Users Only)

⚠️ **WARNING**: Only disable test mode when you're absolutely certain about your email sends.

To disable test mode:
1. Uncheck the "Test Mode" checkbox in publish controls
2. Verify recipient information in console logs
3. Confirm you want to send real emails
4. Click publish

## Commands

- **Sync current post to Ghost**: Publishes the current file to Ghost.io
- **Sync post from Ghost to local**: Downloads a specific post by title
- **Sync all posts from Ghost to local**: Downloads all posts from Ghost.io
- **Open Ghost Sync Status**: Opens the sync status view

## Settings

- **Ghost site URL**: Your Ghost site URL
- **Ghost Admin API Key**: Your Ghost Admin API key (format: id:secret)
- **Articles directory**: Directory where articles are stored
- **Verbose output**: Show detailed output in the console

## Troubleshooting

### Enhanced Error Logging

The plugin includes comprehensive error logging to help with debugging:

**🔍 Detailed API Error Information:**
When Ghost API requests fail, the plugin logs:
- Request URL and method
- Request headers and body
- Response status and text
- Parsed JSON error details
- Specific error messages from Ghost

**📋 Example Error Log:**
```
=== GHOST API ERROR ===
Request URL: https://your-site.ghost.io/ghost/api/admin/posts/
Request Method: POST
Request Headers: { Authorization: 'Ghost ...', Content-Type: 'application/json' }
Request Body: {"posts":[{"title":"My Post",...}]}
Response Status: 422
Response Text: Unprocessable Entity
Response JSON: {
  "errors": [
    {
      "message": "Validation error, cannot save post.",
      "type": "ValidationError",
      "details": "Post title already exists"
    }
  ]
}
=== END GHOST API ERROR ===
```

**🛠️ Context-Aware Error Logging:**
For sync operations, additional context is logged:
- File path and post details
- Whether it's an update or new post
- Ghost post ID and slug
- Operation type (sync to/from Ghost)

### Common Issues

**422 Unprocessable Entity:**
- Usually indicates validation errors (duplicate slug, missing required fields)
- Check the detailed error message in the console logs
- Verify your post title and slug are unique

**401 Unauthorized:**
- Invalid or expired Ghost Admin API key
- Check your API key format: `id:secret`
- Verify the key has admin permissions

**404 Not Found:**
- Post doesn't exist in Ghost (normal for new posts)
- Invalid Ghost site URL
- Check your Ghost site URL in settings

## Development

### Project Structure

The plugin is organized into a clean modular structure:

```
src/
├── main.ts              # Main plugin class
├── api/
│   └── ghost-api.ts     # Ghost API client
├── views/
│   └── sync-status-view.ts  # Sync status view
├── settings/
│   └── settings-tab.ts  # Plugin settings
├── utils/
│   └── content-converter.ts  # Content conversion utilities
└── types/
    └── index.ts         # TypeScript type definitions
```

### Building and Testing

To build the plugin:

```bash
npm run build
```

To run tests:

```bash
npm test
```

## Troubleshooting

- **"Ghost Admin API key not configured"**: Make sure you've entered your API key in plugin settings
- **"Invalid Admin API key format"**: Ensure your API key is in the correct format (id:secret)
- **Connection errors**: Verify your Ghost site URL is correct and accessible
- Check the console (Ctrl+Shift+I) for detailed error messages
- Verify that the reusable Ghost API modules are available in `../lib/` directory

## License

MIT License
