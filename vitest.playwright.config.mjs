import { defineConfig } from 'vitest/config';
import * as fs from 'fs';

// Ensure HAR recordings directory exists
const harDir = './e2e/recordings';
if (!fs.existsSync(harDir)) {
  fs.mkdirSync(harDir, { recursive: true });
}

const isCI = process.env.CI === 'true';

export default defineConfig({
  test: {
    environment: 'node',
    globals: true,
    include: ['e2e/**/*.e2e.ts'],
    exclude: ['tests/**/*'],
    testTimeout: isCI ? 120000 : 60000,  // Reduced timeouts with simplified setup
    hookTimeout: isCI ? 60000 : 30000,
    fileParallelism: true,
    maxConcurrency: isCI ? 1 : 3,        // Can handle more concurrency now
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: isCI,
        isolate: true
      }
    },
    reporter: isCI ? ['verbose', 'junit'] : 'default',
    outputFile: isCI ? {
      junit: './test-results/junit.xml'
    } : undefined
  },
  define: {
    global: 'globalThis'
  }
});
