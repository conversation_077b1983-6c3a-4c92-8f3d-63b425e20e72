import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ice, <PERSON>lugin, SuggestModal, TFile, WorkspaceLeaf } from "obsidian";
import * as path from "path";
import { ContentConverter } from "./utils/content-converter";
import { PropertyMapper } from "./utils/property-mapping";
import { ObsidianGhostAPI } from "./api/ghost-api";
import { SvelteSyncStatusView, VIEW_TYPE_GHOST_SYNC_STATUS } from "./views/sync-status-view";
import { GhostSyncSettingTab } from "./settings/settings-tab";
import { SyncMetadataStorage } from "./services/sync-metadata-storage";
import { SmartSyncService } from "./services/smart-sync-service";
import { ObsidianAppAdapter } from "./services/obsidian-app-adapter";
import { GhostSyncSettings, GhostPost, GhostNewsletter } from "./types";
import { Markdown } from "./markdown";

// Post selection modal
class PostSelectionModal extends SuggestModal<GhostPost> {
  private posts: GhostPost[];
  private onSelect: (post: GhostPost) => void;

  constructor(app: App, posts: GhostPost[], onSelect: (post: GhostPost) => void) {
    super(app);
    this.posts = posts;
    this.onSelect = onSelect;
    this.setPlaceholder("Type to search posts...");
  }

  onOpen() {
    super.onOpen();
    const suggesterEl = this.containerEl.querySelector('.suggester-container');

    if (suggesterEl) {
      suggesterEl.setAttribute('data-modal-type', 'post-selection');
    }
  }

  getSuggestions(query: string): GhostPost[] {
    return this.posts.filter(post =>
      post.title.toLowerCase().includes(query.toLowerCase()) ||
      post.slug.toLowerCase().includes(query.toLowerCase())
    );
  }

  renderSuggestion(post: GhostPost, el: HTMLElement) {
    const container = el.createDiv({ cls: "ghost-post-suggestion" });

    const title = container.createDiv({ cls: "ghost-post-title" });
    title.setText(post.title);

    const meta = container.createDiv({ cls: "ghost-post-meta" });
    const status = post.status === 'published' ? '📄' : '📝';
    const publishedDate = post.published_at ?
      new Date(post.published_at).toLocaleDateString() :
      'Draft';
    const tags = post.tags?.map((t: any) => t.name).join(', ') || 'No tags';
    meta.setText(`${status} ${publishedDate} • ${tags}`);
  }

  onChooseSuggestion(post: GhostPost, evt: MouseEvent | KeyboardEvent) {
    this.onSelect(post);
  }
}

// Title input modal for creating new posts
class TitleInputModal extends Modal {
  private onSubmit: (title: string) => void;
  private title: string = "";

  constructor(app: App, onSubmit: (title: string) => void) {
    super(app);
    this.onSubmit = onSubmit;
  }

  onOpen() {
    const { contentEl } = this;
    contentEl.addClass("ghost-sync-modal");

    // Set data-modal-type on the modal container, not contentEl
    this.containerEl.setAttribute("data-modal-type", "create-post");

    contentEl.createEl("h2", { text: "Create New Post" });

    const inputEl = contentEl.createEl("input", {
      type: "text",
      placeholder: "Enter post title...",
    });
    inputEl.setAttribute("data-input", "post-title");
    inputEl.focus();

    inputEl.addEventListener("input", (e) => {
      this.title = (e.target as HTMLInputElement).value;
    });

    inputEl.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        this.submit();
      } else if (e.key === "Escape") {
        this.close();
      }
    });

    const buttonContainer = contentEl.createDiv();
    buttonContainer.style.display = "flex";
    buttonContainer.style.gap = "10px";
    buttonContainer.style.justifyContent = "flex-end";
    buttonContainer.style.marginTop = "15px";

    const cancelBtn = buttonContainer.createEl("button", { text: "Cancel" });
    cancelBtn.setAttribute("data-action", "cancel");
    cancelBtn.addEventListener("click", () => this.close());

    const submitBtn = buttonContainer.createEl("button", { text: "Create" });
    submitBtn.addClass("mod-cta");
    submitBtn.setAttribute("data-action", "submit");
    submitBtn.addEventListener("click", () => this.submit());
  }

  private submit() {
    if (this.title.trim()) {
      this.onSubmit(this.title.trim());
      this.close();
    }
  }

  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
}

const DEFAULT_SETTINGS: GhostSyncSettings = {
  ghostUrl: "https://your-site.ghost.io",
  ghostAdminApiKey: "",
  articlesDir: "articles",
  verbose: false
};

/**
 * Validate Ghost Sync settings
 * @param settings The settings to validate
 * @returns Object with isValid flag and error message if invalid
 */
function validateSettings(settings: GhostSyncSettings): { isValid: boolean; error?: string } {
  // Check if Ghost URL is provided and looks like a URL
  if (!settings.ghostUrl || settings.ghostUrl === DEFAULT_SETTINGS.ghostUrl) {
    return { isValid: false, error: "Ghost site URL not configured" };
  }

  try {
    new URL(settings.ghostUrl);
  } catch {
    return { isValid: false, error: "Invalid Ghost site URL format" };
  }

  // Check if API key is provided and has correct format (id:secret)
  if (!settings.ghostAdminApiKey) {
    return { isValid: false, error: "Ghost Admin API key not configured" };
  }

  if (!settings.ghostAdminApiKey.includes(':')) {
    return { isValid: false, error: "Ghost Admin API key must be in format 'id:secret'" };
  }

  const [id, secret] = settings.ghostAdminApiKey.split(':');
  if (!id || !secret || id.length < 10 || secret.length < 10) {
    return { isValid: false, error: "Ghost Admin API key format appears invalid (should be long id:secret)" };
  }

  // Check articles directory
  if (!settings.articlesDir || settings.articlesDir.trim() === '') {
    return { isValid: false, error: "Articles directory not configured" };
  }

  return { isValid: true };
}

export default class GhostSyncPlugin extends Plugin {
  settings: GhostSyncSettings;
  private newsletters: GhostNewsletter[] = [];
  private newslettersLoaded: boolean = false;
  public syncMetadata: SyncMetadataStorage;
  public appAdapter: ObsidianAppAdapter;
  public lexicalParser = {
    markdownToLexical: async (markdown: string) => {
      const parser = new Markdown();
      try {
        return await parser.markdownToLexical(markdown);
      } finally {
        parser.destroy();
      }
    },
    lexicalToMarkdown: async (document: any) => {
      const parser = new Markdown();
      try {
        return await parser.lexicalToMarkdown(document);
      } finally {
        parser.destroy();
      }
    }
  };

  async onload() {
    await this.loadSettings();

    // Validate settings but don't prevent plugin from loading
    const validation = validateSettings(this.settings);
    if (!validation.isValid) {
      console.warn(`Ghost Sync: ${validation.error}`);
      // Show a notice after a delay to ensure Obsidian is fully loaded
      setTimeout(() => {
        new Notice(`Ghost Sync: ${validation.error}. Please check plugin settings.`, 8000);
      }, 2000);
    }

    // Initialize app adapter with articles directory
    this.appAdapter = new ObsidianAppAdapter(this.app, this.settings.articlesDir);

    // Initialize sync metadata storage
    this.syncMetadata = new SyncMetadataStorage(this);
    await this.syncMetadata.load();

    // Only load newsletters and create API-dependent services if settings are valid
    if (validation.isValid) {
      // Load newsletters in the background
      this.loadNewsletters();

      // Register the sync status view
      this.registerView(
        VIEW_TYPE_GHOST_SYNC_STATUS,
        (leaf) => new SvelteSyncStatusView(leaf, this)
      );
    } else {
      // Register a minimal sync status view that shows configuration error
      this.registerView(
        VIEW_TYPE_GHOST_SYNC_STATUS,
        (leaf) => new SvelteSyncStatusView(leaf, this, null) // Pass null to indicate no API services
      );
    }



    // Add command for syncing current post to Ghost
    this.addCommand({
      id: "sync-current-to-ghost",
      name: "Sync current post to Ghost",
      callback: () => {
        this.syncCurrentPostToGhost();
      }
    });

    // Add command for syncing current file
    this.addCommand({
      id: "sync-current-file",
      name: "Sync current file",
      callback: () => {
        this.syncCurrentPostToGhost();
      }
    });

    // Add command for browsing and syncing posts from Ghost
    this.addCommand({
      id: "browse-ghost-posts",
      name: "Browse and sync posts from Ghost",
      callback: () => {
        this.browseGhostPosts();
      }
    });



    // Add command for syncing all posts from Ghost
    this.addCommand({
      id: "sync-all-from-ghost",
      name: "Sync all posts from Ghost to local",
      callback: () => {
        this.syncAllFromGhost();
      }
    });

    // Add command for creating new post
    this.addCommand({
      id: "create-new-post",
      name: "Ghost Sync: Create new post",
      callback: () => {
        this.createNewPost();
      }
    });

    // Add command for opening sync status view
    this.addCommand({
      id: "open-sync-status",
      name: "Open Ghost Sync Status",
      callback: () => {
        this.activateSyncStatusView();
      }
    });

    // Add settings tab
    this.addSettingTab(new GhostSyncSettingTab(this.app, this));
  }

  async syncCurrentPostToGhost() {
    // Validate settings first
    const validation = validateSettings(this.settings);
    if (!validation.isValid) {
      new Notice(`Ghost Sync: ${validation.error}. Please check plugin settings.`);
      return;
    }

    // Try multiple ways to get the current file to be more robust
    let file: TFile | null = null;

    // First try to get from active editor (most reliable)
    const activeEditor = this.app.workspace.activeEditor;
    if (activeEditor?.file) {
      file = activeEditor.file;
    } else {
      // Fallback to active markdown view
      const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
      if (activeView?.file) {
        file = activeView.file;
      } else {
        // Last resort: check all markdown views for the most recently active one
        const markdownLeaves = this.app.workspace.getLeavesOfType('markdown');
        for (const leaf of markdownLeaves) {
          if (leaf.view instanceof MarkdownView && leaf.view.file) {
            file = leaf.view.file;
            break;
          }
        }
      }
    }

    if (!file) {
      new Notice("No active markdown file");
      return;
    }

    // Check if the file is in the articles directory
    if (!this.appAdapter.isFileInArticlesDir(file)) {
      new Notice(`File must be in the ${this.appAdapter.getArticlesDir()} directory to sync to Ghost`);
      return;
    }

    let title = '';
    let slug = '';
    let isUpdate = false;

    try {
      // Validate settings
      if (!this.settings.ghostAdminApiKey) {
        new Notice("Ghost Admin API key not configured. Please check plugin settings.");
        return;
      }

      // Read and parse the file content
      const content = await this.appAdapter.readFile(file);
      const { frontMatter, markdownContent } = ContentConverter.parseArticle(content);

      // Normalize frontmatter using the centralized property mapping
      const normalizedFrontMatter = PropertyMapper.normalizeToGhost(frontMatter);

      if (!normalizedFrontMatter.title) {
        new Notice("Could not find title in frontmatter");
        return;
      }

      title = normalizedFrontMatter.title;
      new Notice(`Syncing "${title}" to Ghost...`);

      // Create Ghost API client
      const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

      // Check if we have a UUID mapping for this file
      const existingUuid = this.syncMetadata.getGhostUuid(file);
      let existingPost = null;

      if (existingUuid) {
        // Try to get post by UUID first
        try {
          existingPost = await ghostAPI.getPostById(existingUuid);
        } catch (error: any) {
          // Handle 404 - UUID might be stale
          if (error.message?.includes('404') || error.status === 404) {
            existingPost = null;
          } else {
            throw error;
          }
        }
      }

      // If no UUID or post not found by UUID, treat as new post
      // No more slug-based fallbacks - only use UUID-based tracking
      if (!existingPost) {
        slug = normalizedFrontMatter.slug || ContentConverter.slugify(title);
      }

      // Check if post already exists to determine if this is an update
      isUpdate = !!existingPost;

      // Create post data with proper update flag and existing post data
      const postData = await ContentConverter.createGhostPostData(frontMatter, markdownContent, {
        status: 'published',
        isUpdate: isUpdate,
        existingPost: existingPost
      });

      let result;
      if (isUpdate) {
        result = await ghostAPI.updatePost(postData);
        new Notice(`Updated "${title}" in Ghost`);
      } else {
        result = await ghostAPI.createPost(postData);
        new Notice(`Created "${title}" in Ghost`);
      }

      if (this.settings.verbose) {
        console.log("Ghost sync result:", result);
      }

      // Store UUID mapping and update sync metadata after successful sync
      if (result) {
        if (result.id) {
          await this.syncMetadata.setGhostUuid(file, result.id);
        }
        await this.syncMetadata.setSyncedAt(file, result.updated_at);
        // Longer delay to ensure sync metadata is persisted
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Update local file frontmatter with Ghost response data to ensure sync
      if (result) {
        try {
          // Read current file content
          const currentContent = await this.appAdapter.readFile(file);
          const { frontMatter, markdownContent } = ContentConverter.parseArticle(currentContent);

          // Update frontmatter with Ghost response data using proper property mapping
          const ghostData = PropertyMapper.normalizeToObsidian({
            slug: result.slug,
            status: result.status,
            visibility: result.visibility || 'public',
            created_at: result.created_at,
            updated_at: result.updated_at,
            ...(result.published_at && { published_at: result.published_at }),
            ...(result.newsletter?.name && { newsletter: result.newsletter.name }),
            ...(result.email && { email_sent: 'Yes' }),
            ...(result.tags && { tags: result.tags.map((tag: any) => tag.name) }),
            primary_tag: result.primary_tag?.name || null,
            ...(result.feature_image && { feature_image: result.feature_image }),
            featured: result.featured || false
          });

          const updatedFrontMatter = {
            ...frontMatter,
            ...ghostData
          };

          // Reconstruct the file with updated frontmatter but original content
          const yamlFrontmatter = ContentConverter.objectToYaml(updatedFrontMatter);
          const updatedContent = `---\n${yamlFrontmatter}---\n\n${markdownContent}`;

          // Update the local file
          await this.appAdapter.writeFile(file, updatedContent);

          if (this.settings.verbose) {
            console.log("Updated local file frontmatter with Ghost response data");
          }
        } catch (error) {
          console.error("Error updating local file with Ghost data:", error);
          // Don't fail the whole operation for this
        }
      }

    } catch (error) {
      console.error('=== SYNC TO GHOST ERROR ===');
      console.error('File path:', file.path);
      console.error('Post title:', title);
      console.error('Post slug:', slug);
      console.error('Is update:', isUpdate);
      console.error('Full error:', error);
      console.error('=== END SYNC TO GHOST ERROR ===');

      new Notice(`Error syncing "${title}" to Ghost: ${error.message}`);
    }
  }



  async browseGhostPosts() {
    try {
      // Validate settings
      const validation = validateSettings(this.settings);
      if (!validation.isValid) {
        new Notice(`Ghost Sync: ${validation.error}. Please check plugin settings.`);
        return;
      }

      new Notice("Fetching posts from Ghost...");

      // Create Ghost API client
      const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

      // Get all posts
      const posts = await ghostAPI.getPosts();

      if (posts.length === 0) {
        new Notice("No posts found in Ghost");
        return;
      }

      // Show post selection modal
      const modal = new PostSelectionModal(this.app, posts, async (selectedPost) => {
        try {
          new Notice(`Syncing "${selectedPost.title}" from Ghost...`);

          const filename = selectedPost.slug + '.md';

          // Ensure the articles directory exists
          await this.appAdapter.ensureArticlesDirExists();

          // Check if file already exists
          const existingFile = this.appAdapter.getArticleFile(filename);
          let targetFile: TFile;

          if (existingFile) {
            targetFile = existingFile;
          } else {
            // Create the file first with minimal content to get a TFile object
            const tempContent = `---\ntitle: "${selectedPost.title}"\nslug: "${selectedPost.slug}"\n---\n\n# ${selectedPost.title}\n\nLoading...`;
            targetFile = await this.appAdapter.createArticleFile(filename, tempContent);
          }

          // Use the smart sync service to properly sync from Ghost (handles all properties correctly)
          const smartSyncService = new SmartSyncService({
            ghostAPI: new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey),
            readFile: (file: TFile) => this.appAdapter.readFile(file),
            writeFile: (file: TFile, content: string) => this.appAdapter.writeFile(file, content),
            parseMarkdown: (content: string) => {
              const parsed = ContentConverter.parseArticle(content);
              return { frontMatter: parsed.frontMatter, content: parsed.markdownContent };
            },
            syncMetadata: this.syncMetadata,
            renameFileForTitleChange: async (file: TFile, newTitle: string) => {
              const newSlug = ContentConverter.slugify(newTitle);
              const newFilename = newSlug + '.md';
              const newPath = this.appAdapter.getArticleFilePath(newFilename);

              if (newPath !== file.path) {
                await this.app.fileManager.renameFile(file, newPath);
                return this.appAdapter.getFile(newPath);
              }
              return file;
            }
          });

          await smartSyncService.syncFromGhost(targetFile, selectedPost);

          // Store UUID mapping for future syncs
          if (selectedPost.id) {
            await this.syncMetadata.setGhostUuid(targetFile, selectedPost.id);
          }

          // Update sync metadata with Ghost post data
          await this.syncMetadata.setSyncFromGhost(targetFile, selectedPost.updated_at, selectedPost);

          new Notice(`Synced "${selectedPost.title}" from Ghost`);

          // Open the synced file in the editor
          const leaf = this.app.workspace.getLeaf();
          await leaf.openFile(targetFile);

          // Focus the editor
          this.app.workspace.setActiveLeaf(leaf);

          if (this.settings.verbose) {
            console.log("Ghost sync result:", { post: selectedPost.title, file: targetFile.path });
          }

        } catch (error) {
          console.error("Error syncing selected post:", error);
          new Notice(`Error syncing "${selectedPost.title}": ${error.message}`);
        }
      });

      modal.open();

    } catch (error) {
      console.error("Error fetching posts from Ghost:", error);
      new Notice(`Error fetching posts from Ghost: ${error.message}`);
    }
  }

  async syncAllFromGhost() {
    try {
      // Validate settings
      const validation = validateSettings(this.settings);
      if (!validation.isValid) {
        new Notice(`Ghost Sync: ${validation.error}. Please check plugin settings.`);
        return;
      }

      new Notice("Syncing all posts from Ghost...");

      // Create Ghost API client
      const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);

      // Get all posts
      const posts = await ghostAPI.getPosts();

      if (posts.length === 0) {
        new Notice("No posts found in Ghost");
        return;
      }

      let syncedCount = 0;
      const errors: string[] = [];

      for (const post of posts) {
        try {
          // Convert post to article format
          const articleContent = await ContentConverter.convertGhostPostToArticle(post);
          const filename = post.slug + '.md';

          // Ensure the articles directory exists
          await this.appAdapter.ensureArticlesDirExists();

          // Check if file already exists
          const existingFile = this.appAdapter.getArticleFile(filename);
          let targetFile: TFile;
          if (existingFile) {
            // Update existing file
            targetFile = existingFile;
            await this.appAdapter.writeFile(targetFile, articleContent);
          } else {
            // Create new file
            targetFile = await this.appAdapter.createArticleFile(filename, articleContent);
          }

          // Store UUID mapping for future syncs
          if (post.id) {
            await this.syncMetadata.setGhostUuid(targetFile, post.id);
          }

          syncedCount++;

          if (this.settings.verbose) {
            console.log(`Synced: ${post.title} → ${targetFile.path}`);
          }

        } catch (error) {
          const errorMsg = `Failed to sync "${post.title}": ${error.message}`;
          errors.push(errorMsg);
          console.error(errorMsg, error);
        }
      }

      if (errors.length > 0) {
        new Notice(`Synced ${syncedCount} posts with ${errors.length} errors. Check console for details.`);
        console.error("Sync errors:", errors);
      } else {
        new Notice(`Successfully synced all ${syncedCount} posts from Ghost`);
      }

      if (this.settings.verbose) {
        console.log(`Sync complete: ${syncedCount} synced, ${errors.length} errors`);
      }

    } catch (error) {
      console.error("Error syncing from Ghost:", error);
      new Notice(`Error syncing from Ghost: ${error.message}`);
    }
  }



  /**
   * Get TFile by Ghost UUID
   */
  getFileByUuid(uuid: string): TFile | null {
    const filePath = this.syncMetadata.getFilePathByUuid(uuid);
    if (!filePath) {
      return null;
    }

    const file = this.app.vault.getAbstractFileByPath(filePath);
    return file instanceof TFile ? file : null;
  }

  /**
   * Rename file when post title changes and update UUID mapping
   */
  async renameFileForTitleChange(file: TFile, newTitle: string): Promise<TFile | null> {
    try {
      // Generate new filename from title
      const newSlug = ContentConverter.slugify(newTitle);
      const newFileName = `${newSlug}.md`;
      const newFilePath = this.appAdapter.getArticleFilePath(newFileName);

      // Check if target file already exists
      const existingFile = this.appAdapter.getFile(newFilePath);
      if (existingFile && existingFile.path !== file.path) {
        console.warn(`Cannot rename file: ${newFilePath} already exists`);
        return null;
      }

      // Rename the file
      await this.app.vault.rename(file, newFilePath);

      // Update UUID mapping with new path
      await this.syncMetadata.updateFilePath(file.path, newFilePath);

      // Get the renamed file
      const renamedFile = this.appAdapter.getFile(newFilePath);
      if (renamedFile) {
        console.log(`Renamed file from ${file.path} to ${newFilePath}`);
        return renamedFile;
      } else {
        console.error(`Failed to get renamed file at ${newFilePath}`);
        return null;
      }
    } catch (error) {
      console.error('Error renaming file for title change:', error);
      return null;
    }
  }

  getVaultPath(): string {
    // Try to get the vault path from the adapter
    const adapter = this.app.vault.adapter as any;
    if (adapter.basePath) {
      return adapter.basePath;
    }
    // Fallback - try to use the vault name or current directory
    return ".";
  }

  async createNewPost() {
    // Show title input modal
    const modal = new TitleInputModal(this.app, async (title: string) => {
      await this.createPostWithTitle(title);
    });
    modal.open();
  }

  private async createPostWithTitle(title: string) {
    try {
      // Create slug from title
      const slug = ContentConverter.slugify(title);

      // Check if file already exists
      const filename = slug + '.md';
      const existingFile = this.appAdapter.getArticleFile(filename);

      if (existingFile) {
        new Notice(`File "${filename}" already exists in ${this.appAdapter.getArticlesDir()}`);
        return;
      }

      // Create timestamps
      const now = new Date();
      const createdAt = now.toISOString();

      // Create frontmatter using the centralized property mapping
      const frontmatter = PropertyMapper.normalizeToObsidian({
        title: title,
        slug: slug,
        status: 'draft',
        created_at: createdAt,
        updated_at: createdAt,
        tags: [] as string[],
        newsletter: null
      });

      // Create article content with frontmatter and empty content
      const yamlFrontmatter = ContentConverter.objectToYaml(frontmatter);
      const articleContent = `---\n${yamlFrontmatter}---\n\nWrite your content here.\n`;

      // Create the file using appAdapter
      const file = await this.appAdapter.createArticleFile(filename, articleContent);

      // Open the file
      const leaf = this.app.workspace.getUnpinnedLeaf();
      await leaf.openFile(file);

      new Notice(`Created new post: "${title}"`);

      if (this.settings.verbose) {
        console.log(`Created new post: ${title} → ${file.path}`);
      }

    } catch (error) {
      console.error("Error creating new post:", error);
      new Notice(`Error creating new post: ${error.message}`);
    }
  }

  async activateSyncStatusView() {
    const { workspace } = this.app;

    let leaf: WorkspaceLeaf | null = null;
    const leaves = workspace.getLeavesOfType(VIEW_TYPE_GHOST_SYNC_STATUS);

    if (leaves.length > 0) {
      // A leaf with our view already exists, use that
      leaf = leaves[0];
    } else {
      // Our view could not be found in the workspace, create a new leaf
      // in the right sidebar for it
      leaf = workspace.getRightLeaf(false);
      await leaf.setViewState({ type: VIEW_TYPE_GHOST_SYNC_STATUS, active: true });
    }

    // "Reveal" the leaf in case it is in a collapsed sidebar
    workspace.revealLeaf(leaf);
  }

  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }

  async saveSettings() {
    await this.saveData(this.settings);

    // Update app adapter with new articles directory
    if (this.appAdapter) {
      this.appAdapter.setArticlesDir(this.settings.articlesDir);
    }
  }

  async loadNewsletters(): Promise<void> {
    if (!this.settings.ghostAdminApiKey || this.newslettersLoaded) {
      return;
    }

    try {
      const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);
      this.newsletters = await ghostAPI.getNewsletters();
      this.newslettersLoaded = true;

      if (this.settings.verbose) {
        console.log('Loaded newsletters:', this.newsletters.map(n => n.name));
      }
    } catch (error) {
      console.error('Error loading newsletters:', error);
      this.newsletters = [];
      this.newslettersLoaded = false;
    }
  }

  getNewsletters(): GhostNewsletter[] {
    return this.newsletters;
  }

  getNewsletterBySlug(slug: string): GhostNewsletter | null {
    return this.newsletters.find(newsletter => newsletter.slug === slug) || null;
  }

  getNewsletterByName(name: string): GhostNewsletter | null {
    return this.newsletters.find(newsletter => newsletter.name === name) || null;
  }

  async refreshNewsletters(): Promise<void> {
    this.newslettersLoaded = false;
    await this.loadNewsletters();
  }
}


