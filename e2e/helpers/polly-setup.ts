import { Polly } from '@pollyjs/core';
import FSPersister from '@pollyjs/persister-fs';
import { ObsidianRequestUrlAdapter } from '../adapters/obsidian-request-url-adapter';
import * as path from 'path';
import * as fs from 'fs';

// Register the adapter and persister
Polly.register(ObsidianRequestUrlAdapter);
Polly.register(FSPersister);

export interface PollyTestConfig {
  testName: string;
  mode?: 'record' | 'replay' | 'passthrough';
  recordingsDir?: string;
  recordIfMissing?: boolean;
}

export class PollyTestManager {
  private polly: Polly | null = null;
  private config: PollyTestConfig;

  constructor(config: PollyTestConfig) {
    // Set defaults first
    const defaults = {
      recordingsDir: path.join(process.cwd(), 'e2e/recordings'),
      recordIfMissing: true,
    };

    // Merge config with defaults
    this.config = { ...defaults, ...config };

    // Set mode after config is merged (since getPollyMode depends on config)
    this.config.mode = this.config.mode || this.getPollyMode();
  }

  private getPollyMode(): 'record' | 'replay' | 'passthrough' {
    // Check environment variables for mode override
    const envMode = process.env.POLLY_MODE;
    if (envMode && ['record', 'replay', 'passthrough'].includes(envMode)) {
      return envMode as 'record' | 'replay' | 'passthrough';
    }

    // Check if we're in CI - default to replay
    if (process.env.CI === 'true') {
      return 'replay';
    }

    // Check if recordings exist for this test
    const recordingPath = this.getRecordingPath();
    if (fs.existsSync(recordingPath)) {
      return 'replay';
    }

    // Default to record mode for new tests
    return 'record';
  }

  private getRecordingPath(): string {
    return path.join(this.config.recordingsDir!, `${this.config.testName}.har`);
  }

  async start(): Promise<Polly> {
    if (this.polly) {
      throw new Error('Polly is already started');
    }

    // Ensure recordings directory exists
    await fs.promises.mkdir(this.config.recordingsDir!, { recursive: true });

    console.log(`🎬 Starting Polly for test: ${this.config.testName}`);
    console.log(`📁 Mode: ${this.config.mode}`);
    console.log(`📁 Recordings dir: ${this.config.recordingsDir}`);

    this.polly = new Polly(this.config.testName, {
      mode: this.config.mode!,
      adapters: ['obsidian-request-url'],
      persister: 'fs',
      persisterOptions: {
        fs: {
          recordingsDir: this.config.recordingsDir!,
        },
      },
      recordIfMissing: this.config.recordIfMissing,
      recordFailedRequests: true,
      logging: true,
      matchRequestsBy: {
        method: true,
        headers: false, // Don't match on headers to avoid auth token issues
        body: false,    // Don't match on body to avoid timestamp issues
        order: false,   // Don't require requests to be in the same order
        url: {
          protocol: true,
          username: false,
          password: false,
          hostname: true,
          port: true,
          pathname: true,
          query: true,
          hash: false,
        },
      },
    });

    // Configure server rules for Ghost API
    this.setupGhostAPIRules();

    console.log(`✅ Polly started in ${this.config.mode} mode`);
    return this.polly;
  }

  private setupGhostAPIRules(): void {
    if (!this.polly) return;

    const { server } = this.polly;

    // Configure Ghost API patterns
    const ghostApiPattern = '**/ghost/api/**';

    // In record mode, pass through all requests to record them
    if (this.config.mode === 'record') {
      server.get(ghostApiPattern).passthrough();
      server.post(ghostApiPattern).passthrough();
      server.put(ghostApiPattern).passthrough();
      server.patch(ghostApiPattern).passthrough();
      server.delete(ghostApiPattern).passthrough();
    }

    // In replay mode, requests will be automatically replayed from recordings
    // In passthrough mode, all requests go through normally

    // Add some common request modifications
    server.any(ghostApiPattern).on('beforeRequest', (req) => {
      console.log(`🌐 Ghost API request: ${req.method} ${req.url}`);
    });

    server.any(ghostApiPattern).on('beforeResponse', (req, res) => {
      console.log(`📡 Ghost API response: ${req.method} ${req.url} -> ${res.status}`);
    });
  }

  async stop(): Promise<void> {
    if (!this.polly) {
      return;
    }

    console.log(`🛑 Stopping Polly for test: ${this.config.testName}`);

    try {
      await this.polly.stop();
      this.polly = null;
      console.log(`✅ Polly stopped for test: ${this.config.testName}`);
    } catch (error) {
      console.error(`❌ Error stopping Polly:`, error);
      throw error;
    }
  }

  getPolly(): Polly | null {
    return this.polly;
  }

  isRecording(): boolean {
    return this.config.mode === 'record';
  }

  isReplaying(): boolean {
    return this.config.mode === 'replay';
  }
}

/**
 * Utility function to create and manage Polly for a test
 */
export async function setupPollyForTest(testName: string, config?: Partial<PollyTestConfig>): Promise<PollyTestManager> {
  const manager = new PollyTestManager({
    testName,
    ...config,
  });

  await manager.start();
  return manager;
}

/**
 * Utility function to clean up old recordings
 */
export async function cleanupOldRecordings(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
  const recordingsDir = path.join(process.cwd(), 'e2e/recordings');

  if (!fs.existsSync(recordingsDir)) {
    return;
  }

  const files = await fs.promises.readdir(recordingsDir);
  const now = Date.now();

  for (const file of files) {
    if (!file.endsWith('.har')) continue;

    const filePath = path.join(recordingsDir, file);
    const stats = await fs.promises.stat(filePath);

    if (now - stats.mtime.getTime() > maxAge) {
      console.log(`🗑️ Removing old recording: ${file}`);
      await fs.promises.unlink(filePath);
    }
  }
}
