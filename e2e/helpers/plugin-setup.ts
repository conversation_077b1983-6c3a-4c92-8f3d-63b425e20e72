import type { Page, ElectronApplication } from 'playwright';

/**
 * Shared helper for e2e tests to verify plugin availability
 * This should be called once in beforeAll to ensure the plugin is loaded
 * and avoid redundant checks in individual test functions
 *
 * Updated to work with both CDP and Playwright/Electron setups
 */

/**
 * Wait for async operations to complete
 */
export async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Load environment settings from .env file and process.env
 */
function loadEnvironmentSettings(): { ghostUrl?: string; ghostAdminApiKey?: string } {
  const fs = require('fs');
  const path = require('path');

  const envSettings: { ghostUrl?: string; ghostAdminApiKey?: string } = {};

  // Try to read from .env file
  const envPath = path.resolve('.env');
  if (fs.existsSync(envPath)) {
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            let value = valueParts.join('=').trim();
            // Remove quotes if present
            if ((value.startsWith('"') && value.endsWith('"')) ||
              (value.startsWith("'") && value.endsWith("'"))) {
              value = value.slice(1, -1);
            }

            if (key.trim() === 'GHOST_URL') {
              envSettings.ghostUrl = value;
            } else if (key.trim() === 'GHOST_ADMIN_API_KEY') {
              envSettings.ghostAdminApiKey = value;
            }
          }
        }
      }
    } catch (error) {
      console.log('⚠️ Could not read .env file:', error.message);
    }
  }

  // Override with process.env if available
  if (process.env.GHOST_URL) {
    envSettings.ghostUrl = process.env.GHOST_URL;
  }
  if (process.env.GHOST_ADMIN_API_KEY) {
    envSettings.ghostAdminApiKey = process.env.GHOST_ADMIN_API_KEY;
  }

  return envSettings;
}

/**
 * Setup Obsidian with Playwright/Electron
 * Launches Obsidian, waits for initialization, and enables the ghost-sync plugin
 * @param vaultPath Required vault path for testing
 * @param dataDir Required data directory for testing
 */
export async function setupObsidianElectron(
  vaultPath: string,
  dataDir: string
): Promise<{ electronApp: ElectronApplication; page: Page }> {
  const { _electron: electron } = await import('playwright');
  const path = await import('path');
  const fs = await import('fs');

  // Read environment variables from .env file if it exists
  const envSettings = loadEnvironmentSettings();

  const appPath = path.resolve('./.obsidian-unpacked/main.js');
  const resolvedVaultPath = path.resolve(vaultPath);
  const userDataDir = path.resolve(dataDir);

  // Check if Obsidian is unpacked
  if (!fs.existsSync(appPath)) {
    throw new Error(
      `Unpacked Obsidian not found at ${appPath}. ` +
      'Please run: npm run setup:obsidian-playwright'
    );
  }

  console.log("🚀 Launching Obsidian with Playwright Electron...");

  // Launch using Playwright's native Electron support
  const electronApp = await electron.launch({
    args: [
      appPath,
      '--user-data-dir=' + userDataDir,
      'open',
      `obsidian://open?path=${encodeURIComponent(resolvedVaultPath)}`,
    ],
    timeout: 30000,
    env: {
      ...process.env,
      NODE_ENV: 'test'
    }
  });

  console.log("✅ Obsidian launched successfully");

  // Get the first window - Playwright handles this automatically
  const page = await electronApp.firstWindow();
  console.log("📱 Got main window, title:", await page.title());

  // Wait for Obsidian to be fully loaded
  await page.waitForFunction(() => {
    return typeof (window as any).app !== 'undefined' &&
      (window as any).app.workspace !== undefined;
  }, { timeout: 15000 });

  console.log("✅ Obsidian app object is ready");

  // Enable plugins
  await page.evaluate(() => {
    (window as any).app.plugins.setEnable(true);
  });

  await waitForAsyncOperation(200);

  // Configure plugin settings
  await page.evaluate(async (envSettings) => {
    const testSettings = {
      ghostUrl: envSettings.ghostUrl || "http://localhost:9999",
      ghostAdminApiKey: envSettings.ghostAdminApiKey || "1234567890abcdef1234567890:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
      articlesDir: "articles",
      verbose: false,
      testMode: !envSettings.ghostUrl
    };

    const app = (window as any).app;
    if (app.vault && app.vault.adapter) {
      try {
        await app.vault.adapter.write('.obsidian/plugins/ghost-sync/data.json', JSON.stringify(testSettings));
        console.log("✅ Plugin settings configured");
      } catch (error) {
        console.log("⚠️ Could not write plugin data file:", error.message);
      }
    }
  }, envSettings);

  // Enable the ghost-sync plugin
  await page.evaluate(async () => {
    try {
      await (window as any).app.plugins.enablePlugin('ghost-sync');
      console.log("✅ Ghost-sync plugin enabled");
    } catch (error) {
      console.log("⚠️ Plugin enable error:", error.message);
    }
  });

  await waitForAsyncOperation(500);

  // Apply runtime settings
  await page.evaluate((envSettings) => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    if (plugin) {
      plugin.settings = {
        ghostUrl: envSettings.ghostUrl || "https://test-ghost-instance.example.com",
        ghostAdminApiKey: envSettings.ghostAdminApiKey || "1234567890abcdef1234567890:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
        articlesDir: "articles",
        verbose: false
      };
      plugin.testMode = !envSettings.ghostUrl;
    }
  }, envSettings);

  return { electronApp, page };
}

/**
 * Wait for a success notice to appear
 */
export async function waitForSuccessNotice(page: Page, timeout: number = 5000): Promise<boolean> {
  try {
    await page.waitForFunction(
      () => {
        const notices = document.querySelectorAll('.notice');
        const noticeTexts = Array.from(notices).map(n => n.textContent?.toLowerCase() || '');
        return noticeTexts.some(text =>
          text.includes('sync') ||
          text.includes('success') ||
          text.includes('updated') ||
          text.includes('published') ||
          text.includes('saved')
        );
      },
      {},
      { timeout }
    );
    return true;
  } catch (error) {
    console.log(`No success notice appeared within ${timeout}ms`);
    return false;
  }
}

/**
 * Wait for any modal to appear
 */
export async function waitForModal(page: Page, timeout: number = 5000): Promise<{ found: boolean; type: string }> {
  try {
    await page.waitForSelector('.modal-container, .suggester-container, .modal-backdrop', { timeout });

    const modalType = await page.evaluate(() => {
      if (document.querySelector('.modal-container')) return 'modal-container';
      if (document.querySelector('.suggester-container')) return 'suggester';
      if (document.querySelector('.modal-backdrop')) return 'custom-modal';
      return 'unknown';
    });

    return { found: true, type: modalType };
  } catch (error) {
    return { found: false, type: 'none' };
  }
}

/**
 * Get modal content for interaction
 */
export async function getModalContent(page: Page): Promise<any> {
  return await page.evaluate(() => {
    const modalContainer = document.querySelector('.modal-container');
    if (modalContainer) {
      return { type: 'modal-container', element: modalContainer };
    }

    const suggester = document.querySelector('.suggester-container');
    if (suggester) {
      return { type: 'suggester', element: suggester };
    }

    const modalBackdrop = document.querySelector('.modal-backdrop');
    if (modalBackdrop) {
      return { type: 'custom-modal', element: modalBackdrop };
    }

    return null;
  });
}

/**
 * Reset Obsidian UI state by closing modals and dialogs
 */
export async function resetObsidianUI(page: Page): Promise<void> {
  try {
    // Close modals
    await page.evaluate(() => {
      const modals = document.querySelectorAll('.modal-container, .modal-backdrop, .suggester-container, .prompt, .modal');
      modals.forEach(modal => {
        const closeButton = modal.querySelector('.modal-close, .close, [aria-label="Close"]');
        if (closeButton) {
          (closeButton as HTMLElement).click();
        } else {
          modal.remove();
        }
      });
    });

    // Press escape to close any remaining modals
    await page.keyboard.press('Escape');
    await page.keyboard.press('Escape');

    await waitForAsyncOperation(200);
    console.log('✅ UI reset complete');
  } catch (error) {
    console.log(`⚠️ UI reset failed: ${error.message}`);
  }
}

/**
 * Verify that the Ghost Sync plugin is properly loaded and available
 */
export async function verifyPluginAvailable(page: Page): Promise<void> {
  const pluginCheck = await page.evaluate(() => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    const isEnabled = (window as any).app.plugins.isEnabled('ghost-sync');
    return {
      pluginExists: !!plugin,
      isEnabled: isEnabled,
      hasSettings: !!plugin?.settings
    };
  });

  if (!pluginCheck.pluginExists) {
    console.log('⚠️ Ghost Sync plugin is not loaded');
    return;
  }

  console.log(`✅ Plugin verification passed: enabled=${pluginCheck.isEnabled}, hasSettings=${pluginCheck.hasSettings}`);
}

/**
 * Get plugin instance (assumes plugin availability has been verified)
 * This is a simplified version that doesn't include defensive checks
 */
export async function getPlugin(page: Page): Promise<any> {
  return await page.evaluate(() => {
    return (window as any).app.plugins.plugins['ghost-sync'];
  });
}

/**
 * Get file from vault (assumes plugin availability has been verified)
 * This is a simplified version that doesn't include defensive checks
 */
export async function getFile(page: Page, filePath: string): Promise<any> {
  return await page.evaluate(({ path }) => {
    return (window as any).app.vault.getAbstractFileByPath(path);
  }, { path: filePath });
}

/**
 * Get sync metadata for a file (assumes plugin availability has been verified)
 */
export async function getSyncMetadata(page: Page, filePath: string): Promise<any> {
  return await page.evaluate(async ({ path }) => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    const file = (window as any).app.vault.getAbstractFileByPath(path);

    if (!file) {
      throw new Error(`File not found: ${path}`);
    }

    try {
      const syncedAt = plugin.syncMetadata?.getSyncedAt?.(file);
      const changedAt = plugin.syncMetadata?.getChangedAt?.(file);

      return {
        hasSyncedAt: !!syncedAt,
        syncedAt: syncedAt,
        hasChangedAt: !!changedAt,
        changedAt: changedAt,
        fileExists: true,
        pluginExists: true
      };
    } catch (error) {
      throw new Error(`Error getting sync metadata: ${error.message}`);
    }
  }, { path: filePath });
}

/**
 * Setup request recording/replaying in the Electron context
 * This injects a simple recording mechanism directly into the Electron context
 * where we can access Obsidian's requestUrl through the plugin
 */
export async function setupRequestRecordingInElectronContext(page: Page, testName: string): Promise<void> {
  console.log(`🔌 Setting up request recording in Electron context for test: ${testName}`);

  // Inject recording setup directly into the Electron context
  await page.evaluate(async () => {
    console.log('🎬 Setting up request recording in Electron context...');

    const app = (window as any).app;
    const plugin = app?.plugins?.plugins?.['ghost-sync'];

    if (!plugin) {
      throw new Error('Ghost sync plugin is not available');
    }

    // Access the original requestUrl through the plugin's API instance
    const originalRequestUrl = plugin.api?.makeRequest || plugin.ghostApi?.makeRequest;

    if (!originalRequestUrl) {
      console.log('⚠️ Could not find original requestUrl, will try to intercept at plugin level');
    }

    const recordings: any[] = [];
    let isRecording = process.env.NODE_ENV === 'test'; // Record in test mode

    // Store the recording mechanism on the plugin for easy access
    plugin._testRecording = {
      recordings,
      isRecording,
      originalRequestUrl,

      // Method to intercept requests at the plugin level
      interceptRequest: async function (options: any) {
        console.log(`🌐 Intercepted request: ${options.method || 'GET'} ${options.url}`);

        if (isRecording) {
          console.log(`📼 Recording request: ${options.method || 'GET'} ${options.url}`);

          try {
            // Import requestUrl dynamically in the Electron context
            const { requestUrl } = await import('obsidian');
            const response = await requestUrl(options);

            // Record the interaction
            recordings.push({
              request: {
                method: options.method || 'GET',
                url: options.url,
                headers: options.headers,
                body: options.body
              },
              response: {
                status: response.status,
                headers: response.headers,
                text: response.text,
                json: response.json
              },
              timestamp: new Date().toISOString()
            });

            console.log(`✅ Recorded response: ${response.status} for ${options.url}`);
            return response;
          } catch (error) {
            console.error(`❌ Request failed: ${error.message}`);
            // Still record failed requests
            recordings.push({
              request: {
                method: options.method || 'GET',
                url: options.url,
                headers: options.headers,
                body: options.body
              },
              response: {
                status: 0,
                error: error.message
              },
              timestamp: new Date().toISOString()
            });
            throw error;
          }
        } else {
          // In replay mode, look up recorded responses
          const matchingRecording = recordings.find(r =>
            r.request.method === (options.method || 'GET') &&
            r.request.url === options.url
          );

          if (matchingRecording) {
            console.log(`📼 Replaying response for: ${options.method || 'GET'} ${options.url}`);
            return matchingRecording.response;
          } else {
            console.log(`⚠️ No recorded response found for: ${options.method || 'GET'} ${options.url}`);
            // Fall back to making real request
            const { requestUrl } = await import('obsidian');
            return await requestUrl(options);
          }
        }
      },

      cleanup: () => {
        console.log('🛑 Cleaning up request recording');
        console.log(`📊 Recorded ${recordings.length} interactions`);
        return recordings;
      }
    };

    console.log('✅ Request recording setup complete in Electron context');
  }, testName);

  console.log('✅ Request recording injected and configured in Electron context');
}

/**
 * Cleanup request recording in the Electron context
 */
export async function cleanupRequestRecordingInElectronContext(page: Page): Promise<any[]> {
  console.log('🛑 Cleaning up request recording in Electron context...');

  const recordings = await page.evaluate(() => {
    const app = (window as any).app;
    const plugin = app?.plugins?.plugins?.['ghost-sync'];

    if (plugin?._testRecording?.cleanup) {
      return plugin._testRecording.cleanup();
    }
    return [];
  });

  console.log(`📊 Retrieved ${recordings.length} recorded interactions`);
  return recordings;
}

// Legacy alias for backward compatibility
export const cleanupPollyInElectronContext = cleanupRequestRecordingInElectronContext;
export const setupPollyInElectronContext = setupRequestRecordingInElectronContext;

/**
 * Setup Polly for e2e tests with automatic mode detection
 * Uses dynamic import to avoid global Polly registration for tests that don't need it
 */
export async function setupPollyForE2ETest(testName: string): Promise<any> {
  console.log(`🎬 Setting up Polly for test: ${testName}`);

  // Dynamic import to avoid global registration
  const { setupPollyForTest } = await import('./polly-setup');

  const pollyManager = await setupPollyForTest(testName, {
    // Auto-detect mode based on environment
    mode: process.env.POLLY_MODE as any || (process.env.CI ? 'replay' : 'record'),
    recordIfMissing: true,
  });

  console.log(`✅ Polly setup complete for test: ${testName} in ${pollyManager.isRecording() ? 'record' : 'replay'} mode`);

  return pollyManager;
}
