import type { <PERSON> } from 'playwright';
import { executeCommand, type SharedTestContext } from './shared-context';

/**
 * Settings helpers for e2e tests
 * Simple and fast approach using commands
 */

/**
 * Open Ghost Sync plugin settings
 * Uses command palette to open settings, then clicks Ghost Sync
 */
export async function openGhostSyncSettings(context: SharedTestContext): Promise<void> {
  await executeCommand(context, 'Open Settings');

  await context.page.click('text=Ghost Sync');
  await context.page.waitForSelector('text=Ghost Sync Settings');
}

/**
 * Close settings modal
 */
export async function closeSettings(page: Page): Promise<void> {
  await page.keyboard.press('Escape');
  await page.waitForTimeout(100);
}

/**
 * Fill a setting input field
 */
export async function fillSettingInput(page: Page, placeholder: string, value: string): Promise<void> {
  const input = page.locator(`input[placeholder="${placeholder}"]`);
  await input.clear();
  await input.fill(value);
  await page.waitForTimeout(100);
}
