import type { Page, BrowserContext, Route } from 'playwright'
import * as fs from 'fs'
import * as path from 'path'

/**
 * Ghost API Recorder - Records real Ghost API responses for replay in tests
 * 
 * This system captures actual Ghost API responses and stores them as JSON files
 * that can be easily edited and maintained. Much better than static mocks!
 */

export interface RecordedResponse {
  url: string
  method: string
  status: number
  headers: Record<string, string>
  body: any
  timestamp: string
}

export interface RecordingSession {
  scenario: string
  ghostUrl: string
  responses: RecordedResponse[]
  metadata: {
    recordedAt: string
    version: string
  }
}

export interface GhostAPIRecorderOptions {
  ghostUrl: string
  scenario: string
  recordingsDir?: string
}

export class GhostAPIRecorder {
  private page: Page
  private context: BrowserContext
  private options: GhostAPIRecorderOptions
  private recordingsDir: string
  private recordingFile: string
  private isRecording: boolean = false
  private isReplaying: boolean = false
  private recordedResponses: RecordedResponse[] = []

  constructor(page: Page, context: BrowserContext, options: GhostAPIRecorderOptions) {
    this.page = page
    this.context = context
    this.options = options
    this.recordingsDir = options.recordingsDir || path.join(__dirname, '../recordings')
    this.recordingFile = path.join(this.recordingsDir, `${options.scenario}.json`)
  }

  /**
   * Start recording Ghost API responses
   */
  async startRecording(): Promise<void> {
    if (this.isRecording || this.isReplaying) {
      throw new Error('Already recording or replaying')
    }

    console.log(`🔴 Starting Ghost API recording for scenario: ${this.options.scenario}`)
    console.log(`📁 Recording to: ${this.recordingFile}`)

    this.ensureRecordingsDirectory()
    this.isRecording = true
    this.recordedResponses = []

    const ghostApiPattern = `${this.options.ghostUrl}/ghost/api/**`

    await this.page.route(ghostApiPattern, async (route: Route) => {
      const request = route.request()
      
      console.log(`📡 Recording: ${request.method()} ${request.url()}`)

      // Let the request go through to the real API
      const response = await route.fetch()
      
      // Capture the response
      const responseBody = await response.text()
      let parsedBody: any
      
      try {
        parsedBody = JSON.parse(responseBody)
      } catch {
        parsedBody = responseBody
      }

      const recordedResponse: RecordedResponse = {
        url: request.url(),
        method: request.method(),
        status: response.status(),
        headers: Object.fromEntries(
          Object.entries(response.headers()).filter(([key]) => 
            // Only keep important headers
            ['content-type', 'cache-control', 'etag'].includes(key.toLowerCase())
          )
        ),
        body: parsedBody,
        timestamp: new Date().toISOString()
      }

      this.recordedResponses.push(recordedResponse)

      // Fulfill the route with the real response
      await route.fulfill({
        status: response.status(),
        headers: response.headers(),
        body: responseBody
      })
    })

    console.log(`✅ Ghost API recording started for: ${ghostApiPattern}`)
  }

  /**
   * Stop recording and save responses to file
   */
  async stopRecording(): Promise<void> {
    if (!this.isRecording) {
      throw new Error('Not currently recording')
    }

    console.log(`🛑 Stopping Ghost API recording for scenario: ${this.options.scenario}`)
    console.log(`💾 Saving ${this.recordedResponses.length} responses`)

    const session: RecordingSession = {
      scenario: this.options.scenario,
      ghostUrl: this.options.ghostUrl,
      responses: this.recordedResponses,
      metadata: {
        recordedAt: new Date().toISOString(),
        version: '1.0.0'
      }
    }

    fs.writeFileSync(this.recordingFile, JSON.stringify(session, null, 2))
    
    this.isRecording = false
    this.recordedResponses = []

    console.log(`✅ Recording saved to: ${this.recordingFile}`)
    console.log(`📝 You can now edit the responses in this JSON file as needed`)
  }

  /**
   * Start replaying recorded responses
   */
  async startReplaying(): Promise<void> {
    if (this.isRecording || this.isReplaying) {
      throw new Error('Already recording or replaying')
    }

    if (!this.recordingExists()) {
      throw new Error(
        `Recording not found: ${this.recordingFile}\n` +
        `Run with recording mode first to capture responses`
      )
    }

    console.log(`▶️  Starting Ghost API replay for scenario: ${this.options.scenario}`)
    console.log(`📁 Replaying from: ${this.recordingFile}`)

    const session = this.loadRecording()
    console.log(`📼 Loaded ${session.responses.length} recorded responses`)

    this.isReplaying = true

    const ghostApiPattern = `${this.options.ghostUrl}/ghost/api/**`

    await this.page.route(ghostApiPattern, async (route: Route) => {
      const request = route.request()
      const requestUrl = request.url()
      const requestMethod = request.method()

      console.log(`🔍 Looking for recorded response: ${requestMethod} ${requestUrl}`)

      // Find matching recorded response
      const matchingResponse = session.responses.find(recorded => 
        recorded.url === requestUrl && recorded.method === requestMethod
      )

      if (matchingResponse) {
        console.log(`✅ Found recorded response: ${requestMethod} ${requestUrl}`)
        
        await route.fulfill({
          status: matchingResponse.status,
          headers: matchingResponse.headers,
          body: typeof matchingResponse.body === 'string' 
            ? matchingResponse.body 
            : JSON.stringify(matchingResponse.body)
        })
      } else {
        console.log(`⚠️  No recorded response found for: ${requestMethod} ${requestUrl}`)
        console.log(`Available recordings:`)
        session.responses.forEach(r => console.log(`  - ${r.method} ${r.url}`))
        
        // Fail the request to make it obvious something is missing
        await route.abort('failed')
      }
    })

    console.log(`✅ Ghost API replay started for: ${ghostApiPattern}`)
  }

  /**
   * Stop replaying
   */
  async stopReplaying(): Promise<void> {
    if (!this.isReplaying) {
      return
    }

    console.log(`⏹️  Stopping Ghost API replay for scenario: ${this.options.scenario}`)
    this.isReplaying = false
    
    // Remove all routes for this pattern
    await this.page.unroute(`${this.options.ghostUrl}/ghost/api/**`)
    
    console.log(`✅ Ghost API replay stopped`)
  }

  /**
   * Check if recording file exists
   */
  recordingExists(): boolean {
    return fs.existsSync(this.recordingFile)
  }

  /**
   * Load recording from file
   */
  private loadRecording(): RecordingSession {
    const content = fs.readFileSync(this.recordingFile, 'utf-8')
    return JSON.parse(content)
  }

  /**
   * Ensure recordings directory exists
   */
  private ensureRecordingsDirectory(): void {
    if (!fs.existsSync(this.recordingsDir)) {
      fs.mkdirSync(this.recordingsDir, { recursive: true })
    }
  }

  /**
   * Get recording file path
   */
  getRecordingPath(): string {
    return this.recordingFile
  }

  /**
   * Get recording info
   */
  getRecordingInfo(): { exists: boolean; path: string; responseCount?: number } {
    if (!this.recordingExists()) {
      return { exists: false, path: this.recordingFile }
    }

    const session = this.loadRecording()
    return {
      exists: true,
      path: this.recordingFile,
      responseCount: session.responses.length
    }
  }
}
