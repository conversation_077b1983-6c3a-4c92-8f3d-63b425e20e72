# Playwright Electron E2E Testing

This document describes the Playwright Electron approach for e2e testing and its benefits compared to the Chrome DevTools Protocol (CDP) approach.

## Overview

### Current CDP Approach
- Starts Obsidian with `--remote-debugging-port=9222`
- Connects to running instance via `chromium.connectOverCDP()`
- Requires external process management
- Limited network request recording capabilities

### Playwright Electron Approach (Experimental)
- Uses `electron.launch()` to start Electron apps directly
- Full process lifecycle management
- Built-in HAR recording for network requests
- Better error handling and debugging

## Solution: Unpacked Obsidian Approach

**Status**: ✅ **Working!** We've successfully implemented Playwright Electron support for Obsidian using the approach from [obsidian-trash-explorer](https://github.com/proog/obsidian-trash-explorer).

The solution works around Obsidian's compatibility issues by:
1. **Extracting Obsidian's source files** using `@electron/asar extract`
2. **Launching the unpacked version** with `npx electron`
3. **Using the `obsidian://open?path=` URL scheme** to open specific vaults
4. **Full Playwright Electron API support** with HAR recording

## Benefits

### 1. **Direct Process Control**
```typescript
// Launch Obsidian directly
const electronApp = await electron.launch({
  executablePath: '/Applications/Obsidian.app/Contents/MacOS/Obsidian',
  args: ['--user-data-dir=' + testDataDir],
});

// Get main window
const page = await electronApp.firstWindow();
```

### 2. **Built-in HAR Recording**
```typescript
const electronApp = await electron.launch({
  // ... other options
  recordHar: {
    path: 'e2e/recordings/test.har',
    mode: 'minimal', // Only essential routing info
  },
});
```

### 3. **Better Process Management**
- Automatic cleanup on test completion
- No orphaned processes
- Proper error handling

### 4. **Network Request Recording**
- Records all network requests automatically
- Can replay requests for consistent testing
- Useful for debugging API interactions

## File Structure

```
e2e/
├── specs/
│   ├── playwright-electron.e2e.ts    # New Playwright Electron test
│   └── create-new-post.e2e.ts        # Existing CDP test
├── recordings/                        # HAR files for request recording
│   └── create-post.har
└── PLAYWRIGHT-ELECTRON.md            # This documentation
```

## Setup and Running Tests

### One-Time Setup
```bash
# Install dependencies (includes @electron/asar)
npm install

# Run the setup script (interactive)
npm run setup:obsidian-playwright
# OR directly:
./scripts/setup-obsidian-playwright.sh

# Follow the prompts to:
# 1. Open the test vault in Obsidian
# 2. Enable community plugins
# 3. Enable the Ghost Sync plugin
```

### Running Tests
```bash
# Run Playwright Electron test
npm run test:e2e:playwright

# Run specific test file
npx vitest run --config vitest.e2e.config.mjs e2e/specs/playwright-electron.e2e.ts
```

### What the Setup Does
1. **Unpacks Obsidian** using `@electron/asar extract`
2. **Builds the plugin** (`npm run build`)
3. **Creates symlinks** to plugin files in test vault
4. **Launches Obsidian** for initial vault configuration
5. **Creates `.obsidian-unpacked/`** directory with runnable Obsidian source

## Test Example

The `playwright-electron.e2e.ts` demonstrates:

```typescript
import { _electron as electron } from 'playwright';

describe("Playwright Electron Test", () => {
  let electronApp: ElectronApplication;
  let page: Page;

  beforeAll(async () => {
    // Launch Obsidian with HAR recording
    electronApp = await electron.launch({
      executablePath: '/Applications/Obsidian.app/Contents/MacOS/Obsidian',
      args: ['--user-data-dir=' + testDataDir],
      recordHar: { path: 'recordings/test.har' },
    });

    page = await electronApp.firstWindow();
  });

  afterAll(async () => {
    await electronApp.close(); // Clean shutdown
  });

  test("should create new post", async () => {
    // Test implementation...
  });
});
```

## HAR Recording

### What Gets Recorded
- HTTP requests and responses
- Request/response headers
- Timing information
- Response bodies (configurable)

### Using Recordings
- Debug API interactions
- Replay requests for consistent testing
- Analyze network performance
- Mock external services

### Recording Files
- Saved to `e2e/recordings/`
- Named by test functionality
- Can be viewed in browser dev tools or HAR viewers

## Migration Path

### Phase 1: Parallel Testing ✅
- Keep existing CDP tests
- Add new Playwright Electron tests
- Compare results and stability

### Phase 2: Gradual Migration
- Port critical tests to Playwright Electron
- Validate feature parity
- Update CI/CD pipelines

### Phase 3: Full Migration
- Replace CDP approach entirely
- Update documentation
- Remove CDP-specific code

## Troubleshooting

### Common Issues

1. **Obsidian Won't Launch**
   - Check executable path
   - Verify user data directory permissions
   - Ensure no other Obsidian instances running

2. **Plugin Not Loading**
   - Run setup script: `npm run test:e2e:playwright:setup`
   - Check plugin files in test vault
   - Verify manifest.json is valid

3. **HAR Recording Issues**
   - Ensure recordings directory exists
   - Check file permissions
   - Verify disk space

### Debug Tips

```typescript
// Enable verbose logging
electronApp = await electron.launch({
  // ... other options
  env: { ...process.env, DEBUG: 'pw:*' }
});

// Access Electron main process
await electronApp.evaluate(({ app }) => {
  console.log('App path:', app.getAppPath());
  console.log('Version:', app.getVersion());
});
```

## Next Steps

1. **Test the Implementation**
   ```bash
   npm run test:e2e:playwright
   ```

2. **Review HAR Recording**
   - Check `e2e/recordings/create-post.har`
   - Open in browser dev tools

3. **Port Additional Tests**
   - Start with simple tests
   - Gradually add complex scenarios

4. **Integrate with CI**
   - Update GitHub Actions
   - Add HAR artifact collection
