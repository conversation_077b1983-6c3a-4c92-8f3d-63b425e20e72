/**
 * Demo script showing how the Polly adapter works
 * This can be run independently to test the Polly integration
 */

const { Polly } = require('@pollyjs/core');
const FSPersister = require('@pollyjs/persister-fs');
const Adapter = require('@pollyjs/adapter');
const path = require('path');

// Simple JavaScript version of the Obsidian adapter for demo
class ObsidianRequestUrlAdapter extends Adapter {
  static get id() {
    return 'obsidian-request-url';
  }

  get defaultOptions() {
    return {
      context: globalThis
    };
  }

  onConnect() {
    const { context } = this.options;
    console.log('🔌 Connecting Polly ObsidianRequestUrl adapter');

    this.originalRequestUrl = context.requestUrl;
    if (!this.originalRequestUrl) {
      throw new Error('requestUrl is not available in global scope');
    }

    context.requestUrl = this.wrapRequestUrl.bind(this);
    console.log('✅ Polly ObsidianRequestUrl adapter connected');
  }

  onDisconnect() {
    const { context } = this.options;
    console.log('🔌 Disconnecting Polly ObsidianRequestUrl adapter');

    if (this.originalRequestUrl) {
      context.requestUrl = this.originalRequestUrl;
    }
    console.log('✅ Polly ObsidianRequestUrl adapter disconnected');
  }

  async wrapRequestUrl(options) {
    console.log(`🌐 Polly intercepting requestUrl: ${options.method || 'GET'} ${options.url}`);

    try {
      const pollyResponse = await this.handleRequest({
        url: options.url,
        method: (options.method || 'GET').toUpperCase(),
        headers: this.normalizeHeaders(options.headers || {}),
        body: options.body || undefined,
        requestArguments: { originalOptions: options }
      });

      return this.convertToObsidianResponse(pollyResponse);
    } catch (error) {
      console.error('❌ Error in Polly requestUrl wrapper:', error);
      throw error;
    }
  }

  normalizeHeaders(headers) {
    const normalized = {};
    Object.entries(headers).forEach(([key, value]) => {
      normalized[key.toLowerCase()] = String(value);
    });
    return normalized;
  }

  convertToObsidianResponse(pollyResponse) {
    let json;
    let text = pollyResponse.body || '';

    try {
      json = typeof text === 'string' ? JSON.parse(text) : text;
    } catch {
      json = undefined;
    }

    const headers = {};
    if (pollyResponse.headers) {
      Object.entries(pollyResponse.headers).forEach(([key, value]) => {
        headers[key.toLowerCase()] = String(value);
      });
    }

    return {
      status: pollyResponse.statusCode || pollyResponse.status || 200,
      headers,
      text,
      json,
      arrayBuffer: new ArrayBuffer(0),
    };
  }

  async onFetchResponse(pollyRequest) {
    const { originalOptions } = pollyRequest.requestArguments;
    console.log(`🌐 Making actual request: ${pollyRequest.method} ${pollyRequest.url}`);

    try {
      const response = await this.originalRequestUrl(originalOptions);
      return {
        statusCode: response.status,
        headers: response.headers,
        body: response.text,
      };
    } catch (error) {
      console.error(`❌ Request failed: ${pollyRequest.method} ${pollyRequest.url}:`, error);
      throw error;
    }
  }
}

// Register the adapter and persister
Polly.register(ObsidianRequestUrlAdapter);
Polly.register(FSPersister);

// Mock Obsidian's requestUrl function for demo purposes
global.requestUrl = async function (options) {
  console.log(`🌐 Mock requestUrl called: ${options.method || 'GET'} ${options.url}`);

  // Simulate a Ghost API response
  if (options.url.includes('/ghost/api/')) {
    return {
      status: 200,
      headers: { 'content-type': 'application/json' },
      text: JSON.stringify({
        posts: [{
          id: '123',
          title: 'Test Post',
          slug: 'test-post',
          html: '<p>Test content</p>'
        }]
      }),
      json: {
        posts: [{
          id: '123',
          title: 'Test Post',
          slug: 'test-post',
          html: '<p>Test content</p>'
        }]
      },
      arrayBuffer: new ArrayBuffer(0)
    };
  }

  // Default response
  return {
    status: 200,
    headers: {},
    text: 'OK',
    json: { message: 'OK' },
    arrayBuffer: new ArrayBuffer(0)
  };
};

async function runDemo() {
  console.log('🎬 Starting Polly demo...');

  // Create Polly instance
  const polly = new Polly('polly-demo', {
    mode: 'replay', // Change to 'replay' to use recorded responses
    adapters: ['obsidian-request-url'],
    persister: 'fs',
    persisterOptions: {
      fs: {
        recordingsDir: path.join(__dirname, '../recordings'),
      },
    },
    recordIfMissing: true,
    logging: true,
  });

  try {
    console.log('📡 Making test requests...');

    // Make some test requests that will be intercepted by Polly
    const response1 = await global.requestUrl({
      url: 'https://test-ghost-instance.example.com/ghost/api/v3/content/posts/',
      method: 'GET',
      headers: { 'Authorization': 'Bearer test-token' }
    });

    console.log('✅ Response 1:', response1.status, response1.json?.posts?.[0]?.title);

    const response2 = await global.requestUrl({
      url: 'https://test-ghost-instance.example.com/ghost/api/v3/admin/posts/',
      method: 'POST',
      headers: { 'Authorization': 'Bearer test-token' },
      body: JSON.stringify({ posts: [{ title: 'New Post', html: '<p>Content</p>' }] })
    });

    console.log('✅ Response 2:', response2.status, response2.json?.message);

  } catch (error) {
    console.error('❌ Error during demo:', error);
  } finally {
    // Stop Polly
    await polly.stop();
    console.log('🛑 Polly demo completed');
  }
}

// Run the demo if this file is executed directly
if (require.main === module) {
  runDemo().catch(console.error);
}

module.exports = { runDemo };
