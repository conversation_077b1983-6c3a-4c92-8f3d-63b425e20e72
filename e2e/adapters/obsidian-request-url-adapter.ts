import Adapter from '@pollyjs/adapter';
import type { RequestOptions } from 'obsidian';

/**
 * Polly.js adapter for Obsidian's requestUrl function
 * Enables recording and replaying of HTTP requests made through Obsidian's requestUrl API
 */
export class ObsidianRequestUrlAdapter extends Adapter {
  static get id() {
    return 'obsidian-request-url';
  }

  private originalRequestUrl: any;

  get defaultOptions() {
    return {
      context: globalThis
    };
  }

  onConnect() {
    const { context } = this.options;

    console.log('🔌 Connecting Polly ObsidianRequestUrl adapter');

    // Store the original requestUrl function
    this.originalRequestUrl = context.requestUrl;

    if (!this.originalRequestUrl) {
      throw new Error('requestUrl is not available in global scope. Make sure this adapter is used in an Obsidian environment.');
    }

    // Replace requestUrl with our wrapped version
    context.requestUrl = this.wrapRequestUrl.bind(this);

    console.log('✅ Polly ObsidianRequestUrl adapter connected');
  }

  onDisconnect() {
    const { context } = this.options;

    console.log('🔌 Disconnecting Polly ObsidianRequestUrl adapter');

    // Restore the original requestUrl function
    if (this.originalRequestUrl) {
      context.requestUrl = this.originalRequestUrl;
    }

    console.log('✅ Polly ObsidianRequestUrl adapter disconnected');
  }

  private async wrapRequestUrl(options: RequestOptions): Promise<any> {
    console.log(`🌐 Polly intercepting requestUrl: ${options.method || 'GET'} ${options.url}`);

    try {
      // Use Polly's handleRequest method which handles recording/replaying
      const pollyResponse = await this.handleRequest({
        url: options.url,
        method: (options.method || 'GET').toUpperCase(),
        headers: this.normalizeHeaders(options.headers || {}),
        body: options.body || undefined,
        requestArguments: { originalOptions: options }
      });

      // Convert Polly response back to Obsidian format
      return this.convertToObsidianResponse(pollyResponse);
    } catch (error) {
      console.error('❌ Error in Polly requestUrl wrapper:', error);
      throw error;
    }
  }

  private normalizeHeaders(headers: Record<string, any>): Record<string, string> {
    const normalized: Record<string, string> = {};

    Object.entries(headers).forEach(([key, value]) => {
      normalized[key.toLowerCase()] = String(value);
    });

    return normalized;
  }

  private convertToObsidianResponse(pollyResponse: any) {
    // Parse response body
    let json;
    let text = pollyResponse.body || '';

    try {
      json = typeof text === 'string' ? JSON.parse(text) : text;
    } catch {
      // If parsing fails, json will be undefined
      json = undefined;
    }

    // Convert headers to the format Obsidian expects
    const headers: Record<string, string> = {};
    if (pollyResponse.headers) {
      Object.entries(pollyResponse.headers).forEach(([key, value]) => {
        headers[key.toLowerCase()] = String(value);
      });
    }

    return {
      status: pollyResponse.statusCode || pollyResponse.status || 200,
      headers,
      text,
      json,
      arrayBuffer: new ArrayBuffer(0), // Obsidian expects this property
    };
  }

  // This method is called by Polly when it needs to make the actual HTTP request
  async onFetchResponse(pollyRequest: any): Promise<any> {
    const { originalOptions } = pollyRequest.requestArguments;

    console.log(`🌐 Making actual request: ${pollyRequest.method} ${pollyRequest.url}`);

    try {
      // Use the original requestUrl to make the actual request
      const response = await this.originalRequestUrl(originalOptions);

      return {
        statusCode: response.status,
        headers: response.headers,
        body: response.text,
      };
    } catch (error) {
      console.error(`❌ Request failed: ${pollyRequest.method} ${pollyRequest.url}:`, error);
      throw error;
    }
  }
}

export default ObsidianRequestUrlAdapter;
