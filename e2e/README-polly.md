# Polly.js Integration for Obsidian Ghost Sync E2E Tests

This directory contains a custom Polly.js adapter for recording and replaying HTTP requests made through Obsidian's `requestUrl` function.

## Overview

[Polly.js](https://netflix.github.io/pollyjs/) is a library by Netflix for recording, replaying, and stubbing HTTP interactions. Our custom adapter enables <PERSON> to work with Obsidian's `requestUrl` API, allowing us to:

- **Record** real Ghost API interactions during development
- **Replay** recorded responses in tests (no network calls)
- **Stub** specific scenarios for testing edge cases
- **Debug** and analyze API calls

## Files

- `adapters/obsidian-request-url-adapter.ts` - Custom Polly adapter for Obsidian's requestUrl
- `helpers/polly-setup.ts` - Polly configuration and management utilities
- `recordings/` - Directory containing recorded HTTP interactions (.har files)
- `examples/polly-demo.js` - Standalone demo showing how the adapter works

## Usage

### Basic Setup

```typescript
import { setupPollyForE2ETest } from '../helpers/plugin-setup';

describe("My E2E Test", () => {
  let pollyManager;

  beforeEach(async () => {
    // Setup Polly for request recording/replaying
    pollyManager = await setupPollyForE2ETest('my-test-name');
  });

  afterEach(async () => {
    if (pollyManager) {
      await pollyManager.stop();
    }
  });

  test("should make API calls", async () => {
    // Your test code here - all requestUrl calls will be recorded/replayed
  });
});
```

### Modes

The adapter supports three modes:

1. **Record Mode** (`POLLY_MODE=record`)
   - Makes real HTTP requests
   - Records responses to `.har` files
   - Use when developing new tests or updating API interactions

2. **Replay Mode** (`POLLY_MODE=replay`) - Default
   - Uses recorded responses from `.har` files
   - No network calls made
   - Use for running tests in CI or when you want fast, deterministic tests

3. **Passthrough Mode** (`POLLY_MODE=passthrough`)
   - Makes real HTTP requests but doesn't record
   - Use for debugging or one-off testing

### Environment Variables

- `POLLY_MODE` - Set the mode (`record`, `replay`, `passthrough`)
- `CI=true` - Automatically uses replay mode in CI environments

### Recording Management

Recordings are stored in `e2e/recordings/` as `.har` files. Each test gets its own recording file named after the test.

To update recordings:
```bash
POLLY_MODE=record npm run test:e2e
```

To use recordings (default):
```bash
npm run test:e2e
```

## How It Works

1. **Adapter Registration**: The `ObsidianRequestUrlAdapter` is registered with Polly
2. **Function Replacement**: When connected, the adapter replaces `globalThis.requestUrl` with a wrapped version
3. **Request Interception**: All calls to `requestUrl` are intercepted and handled by Polly
4. **Recording/Replaying**: Depending on the mode, Polly either records the response or replays a recorded one
5. **Response Conversion**: Responses are converted between Polly's format and Obsidian's expected format

## Adapter Implementation

The `ObsidianRequestUrlAdapter` extends Polly's base `Adapter` class and implements:

- `onConnect()` - Replaces `requestUrl` with wrapped version
- `onDisconnect()` - Restores original `requestUrl`
- `onFetchResponse()` - Makes actual HTTP requests when recording
- Request/response format conversion between Obsidian and Polly formats

## Benefits

1. **Faster Tests**: No network calls in replay mode
2. **Deterministic**: Same responses every time
3. **Offline Testing**: Tests work without internet
4. **API Evolution**: Easy to update recordings when APIs change
5. **Debugging**: Inspect recorded requests and responses
6. **Edge Cases**: Easy to test error scenarios by modifying recordings

## Demo

Run the standalone demo to see the adapter in action:

```bash
cd e2e/examples
node polly-demo.js
```

This will create a recording in `e2e/recordings/polly-demo.har` showing how the adapter intercepts and records requests.

## Troubleshooting

### No recordings found
- Make sure you've run tests in record mode first: `POLLY_MODE=record npm run test:e2e`
- Check that the recordings directory exists and contains `.har` files

### Requests not being intercepted
- Verify that the adapter is properly registered and connected
- Check that `requestUrl` is available in the global scope
- Look for console logs showing adapter connection status

### Version mismatches
- Ensure all Polly packages are the same version
- Check that the adapter is compatible with your Polly version

## Future Enhancements

- Support for request matching by headers/body
- Automatic recording cleanup (remove old recordings)
- Integration with test fixtures for common scenarios
- Support for multiple Ghost instances/environments
