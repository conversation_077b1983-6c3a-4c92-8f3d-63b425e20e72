name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build plugin
      run: npm run build

    - name: Setup Obsidian for headless testing
      run: |
        # Install xvfb for virtual display (required for Electron apps in CI)
        sudo apt-get update
        sudo apt-get install -y xvfb

        # Download and extract Obsidian for Linux
        wget https://github.com/obsidianmd/obsidian-releases/releases/download/v1.9.10/Obsidian-1.9.10.AppImage
        chmod +x Obsidian-1.9.10.AppImage

        # Extract AppImage to get the unpacked Electron app
        ./Obsidian-1.9.10.AppImage --appimage-extract

        # Create symlink for easier access
        sudo ln -sf "$(pwd)/squashfs-root/obsidian" /usr/local/bin/obsidian

        # Setup unpacked Obsidian for Playwright tests
        npm run setup:obsidian-playwright

    - name: Run E2E tests in headless mode
      run: |
        # Use xvfb-run for virtual display as recommended by Electron docs
        # -a flag automatically selects a display number
        # --server-args configure Xvfb with appropriate settings for Electron
        xvfb-run -a --server-args="-screen 0 1280x1024x24 -ac -nolisten tcp -dpi 96" npm run test:e2e:ci
      env:
        CI: true
        DISPLAY: ':99'

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: e2e-test-results
        path: |
          test-results/
          e2e/test-environments/
          e2e/recordings/
        retention-days: 7
